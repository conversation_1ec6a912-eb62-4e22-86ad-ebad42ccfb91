import React, { useState, useRef, useEffect } from 'react';
import { Card, Button, Slider, Typography, Space, Divider, Tag, Row, Col } from 'antd';
import { 
  PlayCircleOutlined, 
  PauseCircleOutlined, 
  StepBackwardOutlined, 
  StepForwardOutlined,
  SoundOutlined,
  EyeOutlined,
  EyeInvisibleOutlined
} from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;

interface TranscriptSegment {
  id: string;
  startTime: number;
  endTime: number;
  text: string;
  speaker?: string;
}

interface ListeningPlayerProps {
  title: string;
  topic: string;
  audioUrl?: string;
  transcript: TranscriptSegment[];
  questions?: string[];
  answers?: string[];
}

const ListeningPlayer: React.FC<ListeningPlayerProps> = ({
  title,
  topic,
  audioUrl,
  transcript,
  questions = [],
  answers = []
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(70);
  const [showTranscript, setShowTranscript] = useState(false);
  const [showAnswers, setShowAnswers] = useState(false);
  const [activeSegment, setActiveSegment] = useState<string | null>(null);
  
  const audioRef = useRef<HTMLAudioElement>(null);

  // Sample audio simulation (in real app, this would be actual audio)
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.addEventListener('timeupdate', handleTimeUpdate);
      audioRef.current.addEventListener('loadedmetadata', handleLoadedMetadata);
      audioRef.current.addEventListener('ended', handleEnded);
    }
    
    return () => {
      if (audioRef.current) {
        audioRef.current.removeEventListener('timeupdate', handleTimeUpdate);
        audioRef.current.removeEventListener('loadedmetadata', handleLoadedMetadata);
        audioRef.current.removeEventListener('ended', handleEnded);
      }
    };
  }, []);

  const handleTimeUpdate = () => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime);
      
      // Find active transcript segment
      const active = transcript.find(segment => 
        audioRef.current!.currentTime >= segment.startTime && 
        audioRef.current!.currentTime <= segment.endTime
      );
      setActiveSegment(active?.id || null);
    }
  };

  const handleLoadedMetadata = () => {
    if (audioRef.current) {
      setDuration(audioRef.current.duration);
    }
  };

  const handleEnded = () => {
    setIsPlaying(false);
    setCurrentTime(0);
    setActiveSegment(null);
  };

  const togglePlay = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleSeek = (value: number) => {
    if (audioRef.current) {
      audioRef.current.currentTime = value;
      setCurrentTime(value);
    }
  };

  const handleVolumeChange = (value: number) => {
    setVolume(value);
    if (audioRef.current) {
      audioRef.current.volume = value / 100;
    }
  };

  const skipBackward = () => {
    if (audioRef.current) {
      audioRef.current.currentTime = Math.max(0, audioRef.current.currentTime - 10);
    }
  };

  const skipForward = () => {
    if (audioRef.current) {
      audioRef.current.currentTime = Math.min(duration, audioRef.current.currentTime + 10);
    }
  };

  const jumpToSegment = (startTime: number) => {
    if (audioRef.current) {
      audioRef.current.currentTime = startTime;
      setCurrentTime(startTime);
    }
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <div style={{ padding: '20px' }}>
      <Title level={2}>
        <SoundOutlined /> {title}
      </Title>
      <Tag color="blue" style={{ marginBottom: '20px' }}>{topic}</Tag>

      {/* Audio Player Controls */}
      <Card title="Audio Player" style={{ marginBottom: '20px' }}>
        <audio ref={audioRef} preload="metadata">
          {audioUrl && <source src={audioUrl} type="audio/mpeg" />}
          Your browser does not support the audio element.
        </audio>
        
        <div style={{ marginBottom: '20px' }}>
          <Slider
            value={currentTime}
            max={duration}
            onChange={handleSeek}
            tooltip={{ formatter: formatTime }}
            style={{ margin: '0 20px' }}
          />
          <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: '10px' }}>
            <Text>{formatTime(currentTime)}</Text>
            <Text>{formatTime(duration)}</Text>
          </div>
        </div>

        <Row gutter={16} align="middle">
          <Col>
            <Space size="large">
              <Button 
                type="text" 
                icon={<StepBackwardOutlined />} 
                onClick={skipBackward}
                size="large"
                title="Skip back 10 seconds"
              />
              <Button
                type="primary"
                icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                onClick={togglePlay}
                size="large"
                style={{ fontSize: '24px', height: '60px', width: '60px' }}
              />
              <Button 
                type="text" 
                icon={<StepForwardOutlined />} 
                onClick={skipForward}
                size="large"
                title="Skip forward 10 seconds"
              />
            </Space>
          </Col>
          <Col flex="auto">
            <div style={{ display: 'flex', alignItems: 'center', marginLeft: '20px' }}>
              <SoundOutlined style={{ marginRight: '10px' }} />
              <Slider
                value={volume}
                onChange={handleVolumeChange}
                style={{ width: '100px' }}
                tooltip={{ formatter: (value) => `${value}%` }}
              />
            </div>
          </Col>
        </Row>
      </Card>

      {/* Transcript Section */}
      <Card 
        title="Transcript" 
        extra={
          <Button 
            type="text"
            icon={showTranscript ? <EyeInvisibleOutlined /> : <EyeOutlined />}
            onClick={() => setShowTranscript(!showTranscript)}
          >
            {showTranscript ? 'Hide' : 'Show'} Transcript
          </Button>
        }
        style={{ marginBottom: '20px' }}
      >
        {showTranscript ? (
          <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
            {transcript.map((segment) => (
              <div
                key={segment.id}
                style={{
                  padding: '10px',
                  margin: '5px 0',
                  backgroundColor: activeSegment === segment.id ? '#e6f7ff' : 'transparent',
                  border: activeSegment === segment.id ? '2px solid #1890ff' : '1px solid #f0f0f0',
                  borderRadius: '6px',
                  cursor: 'pointer',
                  fontSize: '16px',
                  lineHeight: '1.6'
                }}
                onClick={() => jumpToSegment(segment.startTime)}
              >
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Text strong style={{ fontSize: '14px', color: '#666' }}>
                    {formatTime(segment.startTime)} - {formatTime(segment.endTime)}
                    {segment.speaker && ` | ${segment.speaker}`}
                  </Text>
                </div>
                <Paragraph style={{ margin: '8px 0 0 0', fontSize: '18px' }}>
                  {segment.text}
                </Paragraph>
              </div>
            ))}
          </div>
        ) : (
          <Text type="secondary" style={{ fontSize: '16px' }}>
            Click "Show Transcript" to reveal the listening transcript. 
            Use this feature to help students understand difficult sections.
          </Text>
        )}
      </Card>

      {/* Questions and Answers */}
      {questions.length > 0 && (
        <Card 
          title="Questions & Answers"
          extra={
            <Button 
              type="text"
              icon={showAnswers ? <EyeInvisibleOutlined /> : <EyeOutlined />}
              onClick={() => setShowAnswers(!showAnswers)}
            >
              {showAnswers ? 'Hide' : 'Show'} Answers
            </Button>
          }
        >
          {questions.map((question, index) => (
            <div key={index} style={{ marginBottom: '15px' }}>
              <Text strong style={{ fontSize: '16px' }}>
                {index + 1}. {question}
              </Text>
              {showAnswers && answers[index] && (
                <div style={{ 
                  marginTop: '8px', 
                  padding: '8px 12px', 
                  backgroundColor: '#f6ffed', 
                  border: '1px solid #b7eb8f',
                  borderRadius: '4px'
                }}>
                  <Text style={{ color: '#52c41a', fontSize: '16px' }}>
                    Answer: {answers[index]}
                  </Text>
                </div>
              )}
            </div>
          ))}
        </Card>
      )}
    </div>
  );
};

export default ListeningPlayer;
