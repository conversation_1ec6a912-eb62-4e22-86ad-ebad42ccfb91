import React from 'react';
import { But<PERSON>, Modal, Typography, message } from 'antd';
import { DownloadOutlined, FileTextOutlined } from '@ant-design/icons';

const { Text, Paragraph } = Typography;

interface VocabularyItem {
  word: string;
  definition: string;
  example: string;
  topic: string;
  level: string;
  pronunciation?: string;
  synonyms?: string[];
}

interface ExportUtilsProps {
  vocabularyList?: VocabularyItem[];
  topic?: string;
  onExport?: () => void;
}

export const generateVocabularyHandout = (vocabularyList: VocabularyItem[], topic?: string): string => {
  const header = `IELTS VOCABULARY LIST${topic ? ` - ${topic.toUpperCase()}` : ''}
${'='.repeat(50)}

`;

  const vocabularySection = vocabularyList
    .sort((a, b) => a.word.localeCompare(b.word))
    .map((item, index) => {
      let entry = `${index + 1}. ${item.word.toUpperCase()}`;
      
      if (item.pronunciation) {
        entry += ` ${item.pronunciation}`;
      }
      
      entry += `\n   Level: ${item.level}`;
      entry += `\n   Definition: ${item.definition}`;
      entry += `\n   Example: "${item.example}"`;
      
      if (item.synonyms && item.synonyms.length > 0) {
        entry += `\n   Synonyms: ${item.synonyms.join(', ')}`;
      }
      
      return entry;
    })
    .join('\n\n');

  const footer = `

${'='.repeat(50)}
Total vocabulary items: ${vocabularyList.length}
Generated on: ${new Date().toLocaleDateString()}

Instructions for students:
- Study these vocabulary items before the lesson
- Practice using each word in your own sentences
- Pay attention to pronunciation and synonyms
- Review examples to understand context usage
`;

  return header + vocabularySection + footer;
};

export const generateReadingHandout = (
  title: string,
  topic: string,
  passage: string,
  questions: Array<{question: string; type: string; options?: string[]}>,
  includeAnswers: boolean = false,
  answers?: string[]
): string => {
  const header = `IELTS READING PRACTICE - ${title.toUpperCase()}
Topic: ${topic}
${'='.repeat(50)}

READING PASSAGE:
${'-'.repeat(20)}

${passage}

QUESTIONS:
${'-'.repeat(20)}

`;

  const questionsSection = questions
    .map((q, index) => {
      let questionText = `${index + 1}. ${q.question}\n`;
      
      if (q.type === 'multiple-choice' && q.options) {
        q.options.forEach((option, optIndex) => {
          questionText += `   ${String.fromCharCode(65 + optIndex)}. ${option}\n`;
        });
      }
      
      if (includeAnswers && answers && answers[index]) {
        questionText += `   ANSWER: ${answers[index]}\n`;
      }
      
      return questionText;
    })
    .join('\n');

  const footer = `
${'='.repeat(50)}
Instructions:
- Read the passage carefully
- Answer all questions
- Time limit: 20 minutes
- Check your answers when finished

Generated on: ${new Date().toLocaleDateString()}
`;

  return header + questionsSection + footer;
};

export const generateListeningHandout = (
  title: string,
  topic: string,
  questions: string[],
  includeTranscript: boolean = false,
  transcript?: Array<{text: string; speaker?: string}>,
  includeAnswers: boolean = false,
  answers?: string[]
): string => {
  const header = `IELTS LISTENING PRACTICE - ${title.toUpperCase()}
Topic: ${topic}
${'='.repeat(50)}

INSTRUCTIONS:
- Listen to the audio recording
- Answer the questions below
- You will hear the recording twice
- Write your answers clearly

QUESTIONS:
${'-'.repeat(20)}

`;

  const questionsSection = questions
    .map((question, index) => {
      let questionText = `${index + 1}. ${question}`;
      
      if (includeAnswers && answers && answers[index]) {
        questionText += `\n   ANSWER: ${answers[index]}`;
      }
      
      return questionText;
    })
    .join('\n\n');

  let transcriptSection = '';
  if (includeTranscript && transcript) {
    transcriptSection = `

TRANSCRIPT:
${'-'.repeat(20)}

${transcript.map(segment => {
  const speaker = segment.speaker ? `${segment.speaker}: ` : '';
  return `${speaker}${segment.text}`;
}).join('\n\n')}`;
  }

  const footer = `

${'='.repeat(50)}
Generated on: ${new Date().toLocaleDateString()}
`;

  return header + questionsSection + transcriptSection + footer;
};

export const downloadTextFile = (content: string, filename: string): void => {
  const element = document.createElement('a');
  const file = new Blob([content], { type: 'text/plain' });
  element.href = URL.createObjectURL(file);
  element.download = filename;
  document.body.appendChild(element);
  element.click();
  document.body.removeChild(element);
  
  message.success(`${filename} downloaded successfully!`);
};

const ExportUtils: React.FC<ExportUtilsProps> = ({ vocabularyList, topic, onExport }) => {
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewContent, setPreviewContent] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');

  const handleVocabularyExport = () => {
    if (!vocabularyList || vocabularyList.length === 0) {
      message.warning('No vocabulary items to export');
      return;
    }

    const content = generateVocabularyHandout(vocabularyList, topic);
    const filename = `IELTS_Vocabulary_${topic || 'All'}_${new Date().toISOString().split('T')[0]}.txt`;
    
    downloadTextFile(content, filename);
    if (onExport) onExport();
  };

  const handlePreview = () => {
    if (!vocabularyList || vocabularyList.length === 0) {
      message.warning('No vocabulary items to preview');
      return;
    }

    const content = generateVocabularyHandout(vocabularyList, topic);
    setPreviewContent(content);
    setPreviewTitle(`Vocabulary Handout Preview - ${topic || 'All Topics'}`);
    setPreviewVisible(true);
  };

  return (
    <>
      <Button.Group>
        <Button icon={<FileTextOutlined />} onClick={handlePreview}>
          Preview
        </Button>
        <Button type="primary" icon={<DownloadOutlined />} onClick={handleVocabularyExport}>
          Export Handout
        </Button>
      </Button.Group>

      <Modal
        title={previewTitle}
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        width={800}
        footer={[
          <Button key="close" onClick={() => setPreviewVisible(false)}>
            Close
          </Button>,
          <Button 
            key="download" 
            type="primary" 
            icon={<DownloadOutlined />}
            onClick={() => {
              handleVocabularyExport();
              setPreviewVisible(false);
            }}
          >
            Download
          </Button>
        ]}
      >
        <div style={{ 
          maxHeight: '500px', 
          overflowY: 'auto',
          backgroundColor: '#fafafa',
          padding: '16px',
          borderRadius: '6px',
          fontFamily: 'monospace',
          fontSize: '12px',
          lineHeight: '1.4'
        }}>
          <pre style={{ margin: 0, whiteSpace: 'pre-wrap' }}>
            {previewContent}
          </pre>
        </div>
      </Modal>
    </>
  );
};

export default ExportUtils;
