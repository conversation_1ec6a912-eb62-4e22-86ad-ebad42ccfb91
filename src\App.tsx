import React, { useState } from 'react';
import { Layout, Menu, Typo<PERSON>, <PERSON><PERSON>, <PERSON> } from 'antd';
import {
  BookOutlined,
  SoundOutlined,
  EditOutlined,
  MessageOutlined,
  UnorderedListOutlined,
  SettingOutlined
} from '@ant-design/icons';
import VocabularyManager from './components/Vocabulary/VocabularyManager';
import ListeningPlayer from './components/Listening/ListeningPlayer';
import ReadingPassage from './components/Reading/ReadingPassage';
import { generateVocabularyHandout, downloadTextFile } from './components/Common/ExportUtils';
import './App.css';

const { Header, Sider, Content } = Layout;
const { Title, Text } = Typography;

// Topic categories for IELTS
const IELTS_TOPICS = [
  'Environment',
  'Technology',
  'Education',
  'Health & Medicine',
  'Travel & Tourism',
  'Work & Career',
  'Society & Culture',
  'Science & Research',
  'Media & Communication',
  'Arts & Entertainment'
];

const App: React.FC = () => {
  const [selectedSkill, setSelectedSkill] = useState<string>('');
  const [selectedTopic, setSelectedTopic] = useState<string>('');
  const [collapsed, setCollapsed] = useState(false);
  const [presentationMode, setPresentationMode] = useState(false);

  const skillMenuItems = [
    {
      key: 'listening',
      icon: <SoundOutlined />,
      label: 'Listening',
    },
    {
      key: 'reading',
      icon: <BookOutlined />,
      label: 'Reading',
    },
    {
      key: 'writing',
      icon: <EditOutlined />,
      label: 'Writing',
    },
    {
      key: 'speaking',
      icon: <MessageOutlined />,
      label: 'Speaking',
    },
    {
      key: 'vocabulary',
      icon: <UnorderedListOutlined />,
      label: 'Vocabulary',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: 'Settings',
    },
  ];

  const renderTopicSelection = () => (
    <div style={{ padding: '20px' }}>
      <Title level={2}>Select Topic for {selectedSkill.charAt(0).toUpperCase() + selectedSkill.slice(1)}</Title>
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', 
        gap: '16px',
        marginTop: '20px'
      }}>
        {IELTS_TOPICS.map(topic => (
          <Card
            key={topic}
            hoverable
            onClick={() => setSelectedTopic(topic)}
            style={{ 
              cursor: 'pointer',
              border: selectedTopic === topic ? '2px solid #1890ff' : '1px solid #d9d9d9'
            }}
          >
            <Card.Meta
              title={topic}
              description={`${selectedSkill.charAt(0).toUpperCase() + selectedSkill.slice(1)} exercises and vocabulary for ${topic.toLowerCase()}`}
            />
          </Card>
        ))}
      </div>
    </div>
  );

  const renderWelcome = () => (
    <div style={{ 
      padding: '40px', 
      textAlign: 'center',
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center'
    }}>
      <Title level={1}>IELTS Teaching Platform</Title>
      <Text style={{ fontSize: '18px', marginBottom: '30px' }}>
        Interactive classroom tool for IELTS preparation with topic-based organization
      </Text>
      <div style={{ marginTop: '20px' }}>
        <Text style={{ fontSize: '16px' }}>
          Select a skill from the sidebar to begin teaching
        </Text>
      </div>
    </div>
  );

  return (
    <Layout style={{ height: '100vh' }}>
      <Sider 
        collapsible 
        collapsed={collapsed} 
        onCollapse={setCollapsed}
        width={250}
        style={{ background: '#001529' }}
      >
        <div style={{ 
          height: '64px', 
          margin: '16px',
          background: 'rgba(255, 255, 255, 0.2)',
          borderRadius: '6px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <Title level={4} style={{ color: 'white', margin: 0 }}>
            {collapsed ? 'IELTS' : 'IELTS Platform'}
          </Title>
        </div>
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[selectedSkill]}
          items={skillMenuItems}
          onClick={({ key }) => {
            setSelectedSkill(key);
            setSelectedTopic(''); // Reset topic when changing skill
          }}
        />
      </Sider>
      
      <Layout>
        <Header style={{ 
          background: '#fff', 
          padding: '0 24px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          borderBottom: '1px solid #f0f0f0'
        }}>
          <Title level={3} style={{ margin: 0 }}>
            {selectedSkill === 'settings' ? 'Settings' : 
             selectedTopic ? `${selectedSkill.charAt(0).toUpperCase() + selectedSkill.slice(1)} - ${selectedTopic}` :
             selectedSkill ? `${selectedSkill.charAt(0).toUpperCase() + selectedSkill.slice(1)}` : 'Welcome'}
          </Title>
          <div>
            <Button type="primary" size="large">
              Presentation Mode
            </Button>
          </div>
        </Header>
        
        <Content style={{ 
          margin: 0,
          background: '#fff',
          overflow: 'auto'
        }}>
          {!selectedSkill || selectedSkill === 'settings' ? renderWelcome() : renderTopicSelection()}
        </Content>
      </Layout>
    </Layout>
  );
};

export default App;
