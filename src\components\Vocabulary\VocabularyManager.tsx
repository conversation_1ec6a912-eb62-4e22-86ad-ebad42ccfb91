import React, { useState, useEffect } from 'react';
import { Card, List, Button, Input, Modal, Form, Select, Typography, Space, Divider, Tag } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, ExportOutlined, BookOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;
const { TextArea } = Input;

// IELTS Topics for vocabulary organization
const IELTS_TOPICS = [
  'Environment',
  'Technology',
  'Education',
  'Health & Medicine',
  'Travel & Tourism',
  'Work & Career',
  'Society & Culture',
  'Science & Research',
  'Media & Communication',
  'Arts & Entertainment'
];

// CEFR Levels for IELTS (B2-C2)
const CEFR_LEVELS = ['B2', 'B2+', 'C1', 'C1+', 'C2'];

interface VocabularyItem {
  id: string;
  word: string;
  definition: string;
  example: string;
  topic: string;
  level: string;
  pronunciation?: string;
  synonyms?: string[];
}

interface VocabularyManagerProps {
  selectedTopic: string;
  onExport: (vocabularyList: VocabularyItem[]) => void;
}

const VocabularyManager: React.FC<VocabularyManagerProps> = ({ selectedTopic, onExport }) => {
  const [vocabularyList, setVocabularyList] = useState<VocabularyItem[]>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingItem, setEditingItem] = useState<VocabularyItem | null>(null);
  const [form] = Form.useForm();
  const [selectedLevel, setSelectedLevel] = useState<string>('all');

  // Sample vocabulary data for demonstration
  useEffect(() => {
    const sampleVocabulary: VocabularyItem[] = [
      {
        id: '1',
        word: 'sustainable',
        definition: 'able to be maintained at a certain rate or level without depleting natural resources',
        example: 'The company adopted sustainable business practices to reduce environmental impact.',
        topic: 'Environment',
        level: 'B2+',
        pronunciation: '/səˈsteɪnəbl/',
        synonyms: ['eco-friendly', 'renewable', 'viable']
      },
      {
        id: '2',
        word: 'innovation',
        definition: 'the action or process of innovating; a new method, idea, product, etc.',
        example: 'Technological innovation has transformed the way we communicate.',
        topic: 'Technology',
        level: 'C1',
        pronunciation: '/ˌɪnəˈveɪʃn/',
        synonyms: ['advancement', 'breakthrough', 'development']
      },
      {
        id: '3',
        word: 'curriculum',
        definition: 'the subjects comprising a course of study in a school or college',
        example: 'The new curriculum includes more emphasis on critical thinking skills.',
        topic: 'Education',
        level: 'B2',
        pronunciation: '/kəˈrɪkjələm/',
        synonyms: ['syllabus', 'program', 'course']
      }
    ];
    setVocabularyList(sampleVocabulary);
  }, []);

  const filteredVocabulary = vocabularyList.filter(item => {
    const topicMatch = selectedTopic === 'all' || item.topic === selectedTopic;
    const levelMatch = selectedLevel === 'all' || item.level === selectedLevel;
    return topicMatch && levelMatch;
  });

  const handleAddVocabulary = () => {
    setEditingItem(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEditVocabulary = (item: VocabularyItem) => {
    setEditingItem(item);
    form.setFieldsValue(item);
    setIsModalVisible(true);
  };

  const handleDeleteVocabulary = (id: string) => {
    setVocabularyList(prev => prev.filter(item => item.id !== id));
  };

  const handleModalOk = () => {
    form.validateFields().then(values => {
      if (editingItem) {
        // Edit existing item
        setVocabularyList(prev => 
          prev.map(item => 
            item.id === editingItem.id 
              ? { ...item, ...values }
              : item
          )
        );
      } else {
        // Add new item
        const newItem: VocabularyItem = {
          id: Date.now().toString(),
          ...values,
          synonyms: values.synonyms ? values.synonyms.split(',').map((s: string) => s.trim()) : []
        };
        setVocabularyList(prev => [...prev, newItem]);
      }
      setIsModalVisible(false);
      form.resetFields();
    });
  };

  const handleExport = () => {
    onExport(filteredVocabulary);
  };

  const getLevelColor = (level: string) => {
    const colors: { [key: string]: string } = {
      'B2': 'blue',
      'B2+': 'cyan',
      'C1': 'green',
      'C1+': 'orange',
      'C2': 'red'
    };
    return colors[level] || 'default';
  };

  return (
    <div style={{ padding: '20px' }}>
      <div style={{ marginBottom: '20px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title level={2}>
          <BookOutlined /> Vocabulary Management
          {selectedTopic !== 'all' && ` - ${selectedTopic}`}
        </Title>
        <Space>
          <Select
            value={selectedLevel}
            onChange={setSelectedLevel}
            style={{ width: 120 }}
            placeholder="Filter by level"
          >
            <Select.Option value="all">All Levels</Select.Option>
            {CEFR_LEVELS.map(level => (
              <Select.Option key={level} value={level}>{level}</Select.Option>
            ))}
          </Select>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAddVocabulary}>
            Add Vocabulary
          </Button>
          <Button icon={<ExportOutlined />} onClick={handleExport}>
            Export List
          </Button>
        </Space>
      </div>

      <Card>
        <List
          dataSource={filteredVocabulary}
          renderItem={(item) => (
            <List.Item
              actions={[
                <Button 
                  type="text" 
                  icon={<EditOutlined />} 
                  onClick={() => handleEditVocabulary(item)}
                />,
                <Button 
                  type="text" 
                  danger 
                  icon={<DeleteOutlined />} 
                  onClick={() => handleDeleteVocabulary(item.id)}
                />
              ]}
            >
              <List.Item.Meta
                title={
                  <Space>
                    <Text strong style={{ fontSize: '18px' }}>{item.word}</Text>
                    <Tag color={getLevelColor(item.level)}>{item.level}</Tag>
                    <Tag>{item.topic}</Tag>
                    {item.pronunciation && (
                      <Text type="secondary" style={{ fontStyle: 'italic' }}>
                        {item.pronunciation}
                      </Text>
                    )}
                  </Space>
                }
                description={
                  <div>
                    <Text>{item.definition}</Text>
                    <Divider type="vertical" />
                    <Text italic>"{item.example}"</Text>
                    {item.synonyms && item.synonyms.length > 0 && (
                      <div style={{ marginTop: '8px' }}>
                        <Text type="secondary">Synonyms: {item.synonyms.join(', ')}</Text>
                      </div>
                    )}
                  </div>
                }
              />
            </List.Item>
          )}
        />
      </Card>

      <Modal
        title={editingItem ? 'Edit Vocabulary' : 'Add New Vocabulary'}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={() => setIsModalVisible(false)}
        width={600}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="word"
            label="Word"
            rules={[{ required: true, message: 'Please enter the word' }]}
          >
            <Input placeholder="Enter the vocabulary word" />
          </Form.Item>
          
          <Form.Item
            name="definition"
            label="Definition"
            rules={[{ required: true, message: 'Please enter the definition' }]}
          >
            <TextArea rows={3} placeholder="Enter the definition" />
          </Form.Item>
          
          <Form.Item
            name="example"
            label="Example Sentence"
            rules={[{ required: true, message: 'Please enter an example sentence' }]}
          >
            <TextArea rows={2} placeholder="Enter an example sentence" />
          </Form.Item>
          
          <Form.Item
            name="topic"
            label="Topic"
            rules={[{ required: true, message: 'Please select a topic' }]}
          >
            <Select placeholder="Select IELTS topic">
              {IELTS_TOPICS.map(topic => (
                <Select.Option key={topic} value={topic}>{topic}</Select.Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item
            name="level"
            label="CEFR Level"
            rules={[{ required: true, message: 'Please select a level' }]}
          >
            <Select placeholder="Select CEFR level">
              {CEFR_LEVELS.map(level => (
                <Select.Option key={level} value={level}>{level}</Select.Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item
            name="pronunciation"
            label="Pronunciation (Optional)"
          >
            <Input placeholder="e.g., /prəˌnʌnsiˈeɪʃn/" />
          </Form.Item>
          
          <Form.Item
            name="synonyms"
            label="Synonyms (Optional)"
          >
            <Input placeholder="Enter synonyms separated by commas" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default VocabularyManager;
