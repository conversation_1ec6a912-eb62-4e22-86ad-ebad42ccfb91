# IELTS Teaching Platform

A desktop application designed for English tutors teaching IELTS preparation in classroom settings. Built with Electron and React for cross-platform compatibility and optimized for presentation via HDMI/smartboard.

## Features

### 🎯 Core IELTS Skills
- **Listening**: Interactive audio player with transcript reveals and answer keys
- **Reading**: Passages with vocabulary hints and comprehension questions
- **Vocabulary**: Topic-based vocabulary management with B2-C2 level focus
- **Writing & Speaking**: (Coming soon)

### 📚 Topic-Based Organization
Content organized around 10 key IELTS topics:
- Environment
- Technology  
- Education
- Health & Medicine
- Travel & Tourism
- Work & Career
- Society & Culture
- Science & Research
- Media & Communication
- Arts & Entertainment

### 🏫 Classroom Features
- **Presentation Mode**: Optimized for large screen display
- **Touch-Friendly Controls**: Large buttons for smartboard interaction
- **Answer Reveals**: Progressive disclosure for teaching
- **Export Handouts**: Generate simple text files for student distribution

## Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn

### Installation
1. Clone or download this repository
2. Install dependencies:
   ```bash
   npm install
   ```

### Running the Application

#### Development Mode
1. Start the development server:
   ```bash
   npm start
   ```
2. In a new terminal, start Electron:
   ```bash
   npm run electron-dev
   ```

#### Production Build
```bash
npm run build
npm run dist
```

## Usage Guide

### For Teachers

#### 1. Starting a Lesson
- Launch the application
- Select a skill from the sidebar (Listening, Reading, Vocabulary)
- Choose a topic from the grid
- Use "Presentation Mode" for classroom display

#### 2. Listening Exercises
- Play audio with large, visible controls
- Use transcript reveal to help students understand difficult sections
- Show/hide answers progressively during discussion
- Skip backward/forward 10 seconds for targeted practice

#### 3. Reading Passages
- Display passages with optional vocabulary highlighting
- Click highlighted words to show definitions and examples
- Use timer for timed practice sessions
- Reveal answers and explanations when ready

#### 4. Vocabulary Management
- Browse topic-based vocabulary lists
- Add new vocabulary items with definitions and examples
- Export vocabulary lists as simple text handouts
- Filter by CEFR level (B2-C2)

#### 5. Exporting Student Materials
- Click "Export Handout" to generate text files
- Files include vocabulary lists, questions, and instructions
- Simple format suitable for printing or digital distribution

### Keyboard Shortcuts
- **Space**: Play/Pause audio (in listening mode)
- **Left/Right Arrow**: Skip backward/forward (in listening mode)
- **F11**: Toggle fullscreen
- **Esc**: Exit presentation mode

## Technical Details

### Built With
- **Electron**: Cross-platform desktop framework
- **React**: User interface library
- **TypeScript**: Type-safe JavaScript
- **Ant Design**: Professional UI components
- **Webpack**: Module bundling and development server

### Project Structure
```
src/
├── components/
│   ├── Listening/          # Audio player and transcript components
│   ├── Reading/            # Reading passage and question components
│   ├── Vocabulary/         # Vocabulary management components
│   └── Common/             # Shared utilities and export functions
├── data/                   # Sample content and data structures
└── App.tsx                 # Main application component
```

### Adding Content
Content is currently stored in `src/data/sampleContent.ts`. To add new content:

1. **Listening Content**: Add transcript segments with timestamps
2. **Reading Content**: Add passages with vocabulary hints and questions
3. **Vocabulary**: Add words with definitions, examples, and CEFR levels

## Customization

### Adding New Topics
Edit the `IELTS_TOPICS` array in `src/App.tsx` to add or modify topics.

### Styling for Different Screen Sizes
Modify `src/App.css` to adjust font sizes and spacing for your specific classroom setup.

### Audio Files
Place audio files in the `public/audio/` directory and reference them in your content data.

## Troubleshooting

### Common Issues

1. **Port 3001 already in use**
   - Kill existing processes: `npx kill-port 3001`
   - Or change port in `webpack.config.js`

2. **Electron window not opening**
   - Ensure development server is running first
   - Check that `NODE_ENV=development` is set

3. **Audio not playing**
   - Ensure audio files are in the correct format (MP3, WAV)
   - Check file paths in content data

### Performance Tips
- Use compressed audio files for faster loading
- Limit vocabulary lists to 50-100 items per topic
- Enable hardware acceleration in Electron for smooth animations

## Contributing

This platform is designed to be easily extensible. To add new features:

1. Create new components in the appropriate directory
2. Add sample content to demonstrate functionality
3. Update the main App.tsx to integrate new components
4. Test with actual classroom scenarios

## License

This project is designed for educational use in IELTS teaching environments.

## Support

For technical issues or feature requests, please refer to the documentation or create an issue in the project repository.

---

**Note**: This platform is optimized for classroom teaching and presentation. For individual student practice, consider implementing user progress tracking and personalized content delivery.
